"use client";

import {
  <PERSON><PERSON><PERSON>,
  Spark<PERSON>,
  Download,
  Edit3,
  Zap,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "../ui/button";

export function HeroSection() {
  const router = useRouter();

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-backgroundPrimary to-backgroundSecondary pt-24 lg:pt-32 pb-16"
    >
      {/* Clean background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Simplified gradient orbs */}
        <div className="absolute top-20 right-20 w-72 h-72 rounded-full bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 blur-3xl"></div>
        <div className="absolute bottom-20 left-20 w-80 h-80 rounded-full bg-gradient-to-r from-brandSecondary/8 to-brandAccent/8 blur-3xl"></div>

        {/* Subtle grid pattern */}
        <div
          className="absolute inset-0 opacity-[0.02]"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgba(59, 130, 246, 0.3) 1px, transparent 0)`,
            backgroundSize: "50px 50px",
          }}
        ></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Animated badge */}
          <div className="inline-flex items-center space-x-2 bg-brandAccent/10 rounded-full px-4 py-2 mb-8 border border-brandAccent/20 opacity-0 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
            <Sparkles className="w-4 h-4 text-brandAccent" />
            <span className="text-sm font-medium text-brandAccent">
              100% Free • No Credit Card Required
            </span>
          </div>

          {/* Animated main heading */}
          <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6">
            <span className="gradient-text block opacity-0 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
              Build Professional
            </span>
            <span className="text-textPrimary block opacity-0 animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
              Portfolios in Minutes
            </span>
          </h1>

          {/* Animated subtitle */}
          <p className="text-lg sm:text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto mb-12 leading-relaxed opacity-0 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
            Create stunning portfolios with our intuitive editor, choose from beautiful themes,
            and export as complete websites. Everything you need to showcase your work professionally.
          </p>

          {/* Animated CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-16 opacity-0 animate-fade-in-up" style={{ animationDelay: '1s' }}>
            <Button
              onClick={() => router.push("/login")}
              className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white font-semibold px-8 py-4 text-lg rounded-xl hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300 hover:scale-105"
            >
              <span className="flex items-center gap-2">
                Start Building Free
                <ArrowRight className="h-5 w-5" />
              </span>
            </Button>

            <Button
              variant="theme"
              onClick={() =>
                document
                  .getElementById("features")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
              className="px-8 py-4 text-lg rounded-xl border-2 border-brandAccent hover:border-brandAccent/50 transition-all duration-300 hover:scale-105"
            >
              See Features
            </Button>
          </div>

          {/* Animated key features */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-brandAccent/30 hover:border-brandAccent/30 transition-all duration-300 hover:scale-105 opacity-0 animate-fade-in-up" style={{ animationDelay: '1.2s' }}>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center mx-auto mb-4">
                <Edit3 className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-brandAccent mb-2">Click & Edit</h3>
              <p className="text-textSecondary text-brandAccent text-sm">Intuitive inline editing</p>
            </div>

            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-brandAccent/30 hover:border-brandAccent/30 transition-all duration-300 hover:scale-105 opacity-0 animate-fade-in-up" style={{ animationDelay: '1.4s' }}>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brandSecondary to-brandAccent flex items-center justify-center mx-auto mb-4">
                <Download className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-brandAccent mb-2">Export HTML</h3>
              <p className="text-textSecondary text-brandAccent text-sm">Complete static websites</p>
            </div>

            <div className="text-center p-6 rounded-xl bg-backgroundSecondary/50 border border-brandAccent/30 hover:border-brandAccent/30 transition-all duration-300 hover:scale-105 opacity-0 animate-fade-in-up" style={{ animationDelay: '1.6s' }}>
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brandAccent to-brandPrimary flex items-center justify-center mx-auto mb-4">
                <Zap className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-brandAccent mb-2">Live Publishing</h3>
              <p className="text-textSecondary text-brandAccent text-sm">Instant deployment</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
