"use client";

import {
  Edit3,
  Share2,
  <PERSON>lette,
  Smartphone,
  ArrowRight,
  Sparkles,
  Globe,
  Download,
} from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";

const features = [
  {
    icon: Edit3,
    title: "Intuitive Inline Editing",
    description:
      "Click directly on any text to edit it in place. No forms, no hassle - just pure WYSIWYG editing experience.",
    color: "from-violet-500 to-purple-500",
    highlight: "WYSIWYG Editor",
  },
  {
    icon: Download,
    title: "Live DOM Capture Export",
    description:
      "Revolutionary technology that captures your actual rendered portfolio, ensuring exported sites match pixel-for-pixel.",
    color: "from-emerald-500 to-teal-500",
    highlight: "Breakthrough Technology",
  },
  {
    icon: Palette,
    title: "Beautiful Themes",
    description:
      "Choose from professionally designed themes including Modern and Creative Minimalist. More themes coming soon!",
    color: "from-purple-500 to-pink-500",
    highlight: "2 Themes Available",
  },
  {
    icon: Share2,
    title: "SEO-Friendly URLs",
    description:
      "Automatically generates clean, professional URLs from your name (e.g., /john-smith) for better discoverability.",
    color: "from-blue-500 to-indigo-500",
    highlight: "SEO Optimized",
  },
  {
    icon: Smartphone,
    title: "Mobile Responsive",
    description:
      "All themes are designed to look stunning on desktops, tablets, and mobile devices with perfect adaptation.",
    color: "from-orange-500 to-red-500",
    highlight: "All Devices",
  },
  {
    icon: Globe,
    title: "Instant Publishing",
    description:
      "Publish your portfolio to a live URL instantly. Share your professional presence with clients and employers immediately.",
    color: "from-cyan-500 to-blue-500",
    highlight: "One-Click Deploy",
  },
];

export function FeaturesSection() {
  const router = useRouter();

  return (
    <section id="features" className="relative py-24 lg:py-32 overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundPrimary via-backgroundSecondary to-backgroundPrimary"></div>

      {/* Multiple layered background effects */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_1200px_at_100%_200px,rgba(99,102,241,0.08),transparent)] dark:bg-[radial-gradient(circle_1200px_at_100%_200px,rgba(99,102,241,0.04),transparent)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_800px_at_0%_80%,rgba(139,92,246,0.06),transparent)] dark:bg-[radial-gradient(circle_800px_at_0%_80%,rgba(139,92,246,0.03),transparent)]"></div>

      {/* Animated background orbs */}
      <div className="absolute top-20 right-10 w-64 h-64 bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5 rounded-full blur-3xl animate-float-slow"></div>
      <div className="absolute bottom-20 left-10 w-80 h-80 bg-gradient-to-r from-brandAccent/4 to-brandPrimary/4 rounded-full blur-3xl animate-float-delayed"></div>

      {/* Subtle grid pattern */}
      <div className="absolute inset-0 bg-grid-pattern opacity-[0.015] animate-grid-float"></div>

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        {/* Clean Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandPrimary/10 border border-brandPrimary/20 mb-8">
            <Sparkles className="w-4 h-4 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Everything You Need
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">
              Powerful Features
            </span>
            <br />
            <span className="text-textPrimary">
              Built for Professionals
            </span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Create stunning portfolios with our intuitive editor, beautiful themes,
            and revolutionary export technology. Everything is free to get you started.
          </p>
        </div>

        {/* Clean Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature) => {
            const IconComponent = feature.icon;
            return (
              <div
                key={feature.title}
                className="group p-8 rounded-2xl bg-backgroundSecondary/50 border border-brandAccent/30 hover:border-brandAccent/30 transition-all duration-300 hover:shadow-lg hover:shadow-brandAccent/5"
              >
                {/* Icon */}
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6`}>
                  <IconComponent className="w-6 h-6 text-white" />
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <h3 className="text-xl font-bold text-textPrimary">
                    {feature.title}
                  </h3>

                  <p className="text-textSecondary leading-relaxed">
                    {feature.description}
                  </p>

                  {/* Highlight badge */}
                  <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-brandAccent/10 border border-brandAccent/20">
                    <div className={`w-2 h-2 rounded-full bg-gradient-to-r ${feature.color}`}></div>
                    <span className="text-xs font-semibold text-brandAccent">
                      {feature.highlight}
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Clean CTA Section */}
        <div className="text-center bg-gradient-to-r from-brandPrimary/5 to-brandSecondary/5 rounded-2xl p-12 border border-brandPrimary/10">
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Ready to Get Started?</span>
          </h3>

          <p className="text-lg text-textSecondary max-w-2xl mx-auto mb-8">
            Create your professional portfolio in minutes. Everything is free to get you started.
          </p>

          <Button
            onClick={() => router.push("/login")}
            className="bg-gradient-to-r from-brandPrimary to-brandSecondary text-white font-semibold px-8 py-4 text-lg rounded-xl hover:shadow-lg hover:shadow-brandPrimary/25 transition-all duration-300"
          >
            <span className="flex items-center gap-2">
              Start Building Free
              <ArrowRight className="w-5 h-5" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
}
