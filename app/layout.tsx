import type { Metada<PERSON> } from "next";
import "./globals.css"; // Your globals.css should be imported here
// Import compiled theme CSS files (contains latest component styles)
import "../themes/modern/modern-modular.css";
import "../themes/creative-minimalist/creative-minimalist-compiled.css";//since this theme was not build automatically need to import it manually the compiled version
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/contexts/theme-provider";
import { GeistSans } from "geist/font/sans"; // Import the sans-serif font
import { Geist<PERSON><PERSON> } from "geist/font/mono"; // Import the monospaced font
import { AuthProvider } from "@/contexts/auth-provider";
import { QueryProvider } from "@/contexts/query-provider";


export const metadata: Metadata = {
  title: "Profolify - Build Your Professional Portfolio",
  description: "Create and share your professional portfolio and resume with ease.",
  keywords: ["portfolio", "resume", "professional", "career", "showcase", "personal website"],
  authors: [{ name: "Profolify" }],
  creator: "Profolify",
  publisher: "Profolify",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: "/favicon.ico", sizes: "any" },
      { url: "/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon-32x32.png", sizes: "32x32", type: "image/png" },
    ],
    shortcut: "/favicon-16x16.png",
    apple: "/apple-touch-icon.png",
    other: [
      {
        rel: "android-chrome-192x192",
        url: "/android-chrome-192x192.png",
      },
      {
        rel: "android-chrome-512x512",
        url: "/android-chrome-512x512.png",
      },
    ],
  },
  manifest: "/site.webmanifest",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://profolify.com",
    title: "Profolify - Build Your Professional Portfolio",
    description: "Create and share your professional portfolio and resume with ease.",
    siteName: "Profolify",
  },
  twitter: {
    card: "summary_large_image",
    title: "Profolify - Build Your Professional Portfolio",
    description: "Create and share your professional portfolio and resume with ease.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`${GeistSans.variable} ${GeistMono.variable}`} suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/site.webmanifest" />
        <meta name="theme-color" content="#8B5CF6" />
        <meta name="msapplication-TileColor" content="#8B5CF6" />
      </head>
      <body >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <AuthProvider>
            <QueryProvider>
              {children}
              <Toaster />
            </QueryProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}